---
- name: Deploy and configure Gargantua router containers
  hosts: gargantua
  become: true
  gather_facts: true
  
  vars:
    # 通用配置
    packages_to_install:
      - curl
      - wget
      - htop
      - vim
      - iptables-persistent
      - netfilter-persistent
      - iproute2
      - iputils-ping
      - net-tools
      - tcpdump
      - nftables
    
    # 系统服务
    services_to_enable:
      - ssh
      - systemd-resolved
      - netfilter-persistent
  
  pre_tasks:
    - name: Update apt cache
      apt:
        update_cache: yes
        cache_valid_time: 3600
      when: ansible_os_family == "Debian"
    
    - name: Set timezone
      timezone:
        name: "{{ timezone | default('Asia/Shanghai') }}"
    
    - name: Set locale
      locale_gen:
        name: "{{ locale | default('zh_CN.UTF-8') }}"
        state: present
      when: ansible_os_family == "Debian"
  
  tasks:
    - name: Install required packages
      apt:
        name: "{{ packages_to_install }}"
        state: present
      when: ansible_os_family == "Debian"
    
    - name: Enable required services
      systemd:
        name: "{{ item }}"
        enabled: yes
        state: started
      loop: "{{ services_to_enable }}"
    
    - name: Configure IP forwarding
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      loop:
        - { name: "net.ipv4.ip_forward", value: "1" }
        - { name: "net.ipv6.conf.all.forwarding", value: "1" }
        - { name: "net.ipv4.conf.all.accept_redirects", value: "0" }
        - { name: "net.ipv4.conf.all.send_redirects", value: "0" }
      when: enable_ip_forward is defined and enable_ip_forward
    
    - name: Create iptables directory
      file:
        path: /etc/iptables
        state: directory
        mode: '0755'
    
    - name: Configure basic firewall rules
      blockinfile:
        path: /etc/iptables/rules.v4
        create: yes
        block: |
          *filter
          :INPUT ACCEPT [0:0]
          :FORWARD ACCEPT [0:0]
          :OUTPUT ACCEPT [0:0]
          
          # Allow loopback
          -A INPUT -i lo -j ACCEPT
          
          # Allow established connections
          -A INPUT -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
          
          # Allow SSH
          -A INPUT -p tcp --dport 22 -j ACCEPT
          
          # Allow local subnet
          -A INPUT -s {{ local_subnet }} -j ACCEPT
          
          # Allow Tailscale
          -A INPUT -i tailscale0 -j ACCEPT
          -A FORWARD -i tailscale0 -j ACCEPT
          -A FORWARD -o tailscale0 -j ACCEPT
          
          COMMIT
          
          *nat
          :PREROUTING ACCEPT [0:0]
          :INPUT ACCEPT [0:0]
          :OUTPUT ACCEPT [0:0]
          :POSTROUTING ACCEPT [0:0]
          
          # NAT for local subnet through Tailscale
          -A POSTROUTING -s {{ local_subnet }} -o tailscale0 -j MASQUERADE
          
          COMMIT
      when: firewall_enabled is defined and firewall_enabled
      notify: restart netfilter-persistent
    
    - name: Configure IPv6 firewall rules
      blockinfile:
        path: /etc/iptables/rules.v6
        create: yes
        block: |
          *filter
          :INPUT ACCEPT [0:0]
          :FORWARD ACCEPT [0:0]
          :OUTPUT ACCEPT [0:0]
          
          # Allow loopback
          -A INPUT -i lo -j ACCEPT
          
          # Allow established connections
          -A INPUT -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
          
          # Allow SSH
          -A INPUT -p tcp --dport 22 -j ACCEPT
          
          # Allow Tailscale
          -A INPUT -i tailscale0 -j ACCEPT
          -A FORWARD -i tailscale0 -j ACCEPT
          -A FORWARD -o tailscale0 -j ACCEPT
          
          COMMIT
      when: firewall_enabled is defined and firewall_enabled
      notify: restart netfilter-persistent
    
    - name: Create router status script
      copy:
        dest: /usr/local/bin/gargantua-status
        mode: '0755'
        content: |
          #!/bin/bash
          echo "=== Gargantua Router Status ==="
          echo "Node: {{ inventory_hostname }}"
          echo "Location: {{ location | upper }}"
          echo "Local Subnet: {{ local_subnet }}"
          echo "Gateway IP: {{ gateway_ip }}"
          echo ""
          echo "=== Network Interfaces ==="
          ip addr show
          echo ""
          echo "=== Routing Table ==="
          ip route show
          echo ""
          echo "=== Tailscale Status ==="
          tailscale status
          echo ""
          echo "=== Firewall Rules ==="
          iptables -L -n
          echo ""
          echo "=== NAT Rules ==="
          iptables -t nat -L -n
    
    - name: Create router info file
      copy:
        dest: /etc/gargantua-info
        content: |
          GARGANTUA_LOCATION={{ location }}
          GARGANTUA_REGION={{ region }}
          GARGANTUA_SUBNET={{ local_subnet }}
          GARGANTUA_GATEWAY={{ gateway_ip }}
          GARGANTUA_ROLE=router
          GARGANTUA_VERSION=1.0.0
  
  handlers:
    - name: restart netfilter-persistent
      systemd:
        name: netfilter-persistent
        state: restarted
  
  roles:
    - tailscale

- name: Post-deployment verification
  hosts: gargantua
  become: true
  gather_facts: false
  
  tasks:
    - name: Verify Tailscale connection
      command: tailscale status --json
      register: tailscale_status_check
      failed_when: false
      changed_when: false
    
    - name: Display Tailscale status
      debug:
        msg: "Tailscale status: {{ (tailscale_status_check.stdout | from_json).BackendState if tailscale_status_check.rc == 0 else 'Failed to get status' }}"
    
    - name: Verify IP forwarding
      command: sysctl net.ipv4.ip_forward
      register: ip_forward_check
      changed_when: false
    
    - name: Display IP forwarding status
      debug:
        msg: "IP forwarding: {{ ip_forward_check.stdout }}"
    
    - name: Check router status
      command: /usr/local/bin/gargantua-status
      register: router_status
      changed_when: false
    
    - name: Display router summary
      debug:
        msg: |
          Gargantua router {{ inventory_hostname }} deployed successfully!
          Location: {{ location }}
          Subnet: {{ local_subnet }}
          Gateway: {{ gateway_ip }}
          Tailscale routes: {{ tailscale_advertise_routes | join(', ') if tailscale_advertise_routes is defined else 'None' }}
